import React from "react";
import clsx from "clsx";

const SlideTextButton = ({ 
  id, 
  title, 
  rightIcon, 
  leftIcon, 
  containerClass = "",
  onClick
}) => {
  return (
    <button
      id={id}
      className={clsx(
        "group relative z-10 w-fit cursor-pointer overflow-hidden rounded-full bg-violet-50 px-7 py-3 text-black",
        containerClass
      )}
      onClick={onClick}
    >
      {leftIcon && (
        <span className="relative mr-2 inline-flex">{leftIcon}</span>
      )}

      <span className="relative inline-flex overflow-hidden font-general text-xs uppercase">
        <div className="translate-y-0 skew-y-0 transition duration-500 group-hover:translate-y-[-160%] group-hover:skew-y-12">
          {title}
        </div>
        <div className="absolute translate-y-[164%] skew-y-12 transition duration-500 group-hover:translate-y-0 group-hover:skew-y-0">
          {title}
        </div>
      </span>

      {rightIcon && (
        <span className="relative ml-2 inline-flex">{rightIcon}</span>
      )}
    </button>
  );
};

export default SlideTextButton;