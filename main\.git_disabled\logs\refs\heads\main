0000000000000000000000000000000000000000 da4eca7eb98fb39ddea7eab5ed7a06a4fa2ab878 KoalaCoding05 <<EMAIL>> 1748067692 +0530	commit (initial): first commit
da4eca7eb98fb39ddea7eab5ed7a06a4fa2ab878 da4eca7eb98fb39ddea7eab5ed7a06a4fa2ab878 KoalaCoding05 <<EMAIL>> 1748067707 +0530	Branch: renamed refs/heads/master to refs/heads/main
da4eca7eb98fb39ddea7eab5ed7a06a4fa2ab878 75d44a142fdad998b4a48c615ad26323c38e5fc0 KoalaCoding05 <<EMAIL>> 1748067788 +0530	commit: skeleton
75d44a142fdad998b4a48c615ad26323c38e5fc0 004f686f0e618f2dbfdeef52d8dc8c059e305928 KoalaCoding05 <<EMAIL>> 1748085351 +0530	commit: Images add
004f686f0e618f2dbfdeef52d8dc8c059e305928 42797a595e8bffab8cb18bb875a82cebabdde8f4 KoalaCoding05 <<EMAIL>> 1748087298 +0530	commit: subtle animation
42797a595e8bffab8cb18bb875a82cebabdde8f4 326aa5191ec0739a10f3aa85b271c531cf57ca6a KoalaCoding05 <<EMAIL>> 1748371742 +0530	commit: Section2
326aa5191ec0739a10f3aa85b271c531cf57ca6a d588db0dc331f57d53452968ada0e864bfe0f1c7 KoalaCoding05 <<EMAIL>> 1748546608 +0530	commit: Section 2
d588db0dc331f57d53452968ada0e864bfe0f1c7 fec2ea8b903047ded21dabcb539ecea74b700ad8 KoalaCoding05 <<EMAIL>> 1748548024 +0530	commit: minor fixes
fec2ea8b903047ded21dabcb539ecea74b700ad8 521407f725c78b01239be285cef15c2f99cb8715 KoalaCoding05 <<EMAIL>> 1748633033 +0530	commit: New files
521407f725c78b01239be285cef15c2f99cb8715 c57ae56d4e58e7787d013040cf925588e05dd140 KoalaCoding05 <<EMAIL>> 1748635096 +0530	commit: Update project name in package.json and add new model configuration
c57ae56d4e58e7787d013040cf925588e05dd140 521407f725c78b01239be285cef15c2f99cb8715 KoalaCoding05 <<EMAIL>> 1748693083 +0530	reset: moving to origin/main
521407f725c78b01239be285cef15c2f99cb8715 d04f17fb98c1757e17f975c1744e5d0d64707afd KoalaCoding05 <<EMAIL>> 1748693413 +0530	commit: Add MobileSquareButton component and enhance FlipCard interactions
d04f17fb98c1757e17f975c1744e5d0d64707afd 71e5827f5a548121e2f24c48e8b298ee03a9ecf9 KoalaCoding05 <<EMAIL>> 1748694668 +0530	commit: Add BottomNav component and enhance FlipCard and Hero functionalities
71e5827f5a548121e2f24c48e8b298ee03a9ecf9 9f4beaaf38c5d4e110b02bba20e804a3c7f9ee2e KoalaCoding05 <<EMAIL>> 1748694917 +0530	commit: Refactor BottomNav component for improved styling and layout
9f4beaaf38c5d4e110b02bba20e804a3c7f9ee2e 2684052eb8021f722efbd02e11ee6e35c67e2881 KoalaCoding05 <<EMAIL>> 1748695149 +0530	commit: Update FlipCard component for hover effects and media handling; adjust Navbar button visibility
2684052eb8021f722efbd02e11ee6e35c67e2881 ecbe8d6230346c0e751982fc21f051b91baac435 KoalaCoding05 <<EMAIL>> 1748696178 +0530	commit: Update dependencies and enhance styles; add framer-motion, typescript, and loader styles
ecbe8d6230346c0e751982fc21f051b91baac435 d0ccae54b1365d0198d431c3dbde2b525f634d5f KoalaCoding05 <<EMAIL>> 1748696286 +0530	commit: Add Loading component with GSAP animations and responsive design
d0ccae54b1365d0198d431c3dbde2b525f634d5f ba616f3e422ec7f1539c74dfae61ebe14c718335 KoalaCoding05 <<EMAIL>> 1748696401 +0530	commit: Replace Loader component with Loading component in Hero for consistency
ba616f3e422ec7f1539c74dfae61ebe14c718335 b3f48e2b0e6c4f58fe3f539dcf1d37983cbd4ad7 KoalaCoding05 <<EMAIL>> 1748717154 +0530	commit: Refactor Loading component to remove unused props and improve loader visibility; add show state for conditional rendering
b3f48e2b0e6c4f58fe3f539dcf1d37983cbd4ad7 b1883c6a592ed8abdf2b96c4616daa6430d1170b KoalaCoding05 <<EMAIL>> 1748851595 +0530	commit: feat: Implement loading screen with enhanced visuals and animations
b1883c6a592ed8abdf2b96c4616daa6430d1170b d3077cc5a7180b3c9dfc21f7fc9ac6f7512cb98c KoalaCoding05 <<EMAIL>> 1748855646 +0530	commit: feat: Add Homepage components including Footer, GameFeatures, Hero, Navbar, SingleProduct, and Story with animations and responsive design
d3077cc5a7180b3c9dfc21f7fc9ac6f7512cb98c be96af40168ff148b1b4ab22d59302d8cfbf7c96 KoalaCoding05 <<EMAIL>> 1748856812 +0530	commit: feat: Refactor Navbar component and update loading styles for improved audio control and visual indicators
be96af40168ff148b1b4ab22d59302d8cfbf7c96 0e8dda933e5766203cadcb6e41b546eb6ae9f0a0 KoalaCoding05 <<EMAIL>> 1748857085 +0530	commit: feat: Add textColor prop to AnimatedTitle for customizable text styling
0e8dda933e5766203cadcb6e41b546eb6ae9f0a0 f5d4196b08513512a3e5c398c3301276c5066390 KoalaCoding05 <<EMAIL>> 1748857224 +0530	commit: feat: Update AnimatedTitle containerClass for improved text sizing and responsiveness
f5d4196b08513512a3e5c398c3301276c5066390 2c05ab5cdc9d17cfe164cc8906a947022e536549 KoalaCoding05 <<EMAIL>> 1748859081 +0530	commit: feat: Update SingleProduct component for improved text sizing and responsiveness; add media query for animated title
2c05ab5cdc9d17cfe164cc8906a947022e536549 14333dd8873ae918c525240f3a3ff91bb8eed35f KoalaCoding05 <<EMAIL>> 1750188782 +0530	commit: Add new pages for Games, How It Works, Pricing, and Support with responsive design and animations
