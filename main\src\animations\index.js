// Export all animation hooks
export * from './hooks/useParallaxScroll';
export * from './hooks/useImageReveal';
export * from './hooks/useScrollBasedVideo';
export * from './hooks/useClipPathExpand';
export * from './hooks/useMouseTrailer';
export * from './hooks/useMagneticEffect';
export * from './hooks/useScrollProgress';
export * from './hooks/useTextGradient';

// Export all animation components
export { default as ParallaxSection } from './components/ParallaxSection';
export { default as ImageReveal } from './components/ImageReveal';
export { default as ScrollVideo } from './components/ScrollVideo';
export { default as ClipPathExpand } from './components/ClipPathExpand';
export { default as MouseTrailer } from './components/MouseTrailer';
export { default as MagneticButton } from './components/MagneticButton';
export { default as ScrollProgressBar } from './components/ScrollProgressBar';
export { default as GradientText } from './components/GradientText';
export { default as TextWave } from './components/TextWave';
export { default as NavHoverEffect } from './components/NavHoverEffect';
export { default as HoverCard } from './components/HoverCard';
export { default as SplitTextClipReveal } from './components/SplitTextClipReveal';
export { default as HeroScrollEffect } from './components/HeroScrollEffect';



