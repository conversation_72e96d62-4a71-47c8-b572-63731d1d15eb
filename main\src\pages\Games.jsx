import React, { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import GameHero from '../components/Games/GameHero';
import GameGrid from '../components/Games/GameGrid';
import ContactSection from '../components/Games/ContactSection';
import FloatingParticles from '../components/Games/FloatingParticles';
import MobileActionButtons from '../components/Games/MobileActionButtons';
import MobileHelpSection from '../components/Games/MobileHelpSection';

const Games = () => {
  const [selectedGame, setSelectedGame] = useState(null);
  const { scrollYProgress } = useScroll();

  // Transform values for scroll animations
  const heroY = useTransform(scrollYProgress, [0, 0.3], [0, -100]);
  const heroOpacity = useTransform(scrollYProgress, [0, 0.3], [1, 0]);

  return (
    <div className="min-h-screen bg-blue-50 relative overflow-hidden pb-20 md:pb-0">
      {/* Floating Particles */}
      <FloatingParticles />

      {/* Hero Section */}
      <motion.div
        style={{ y: heroY, opacity: heroOpacity }}
        className="relative z-10"
      >
        <GameHero />
      </motion.div>

      {/* Mobile Action Buttons */}
      <div className="md:hidden relative z-10">
        <MobileActionButtons />
      </div>

      {/* Game Grid Section */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true, margin: "-100px" }}
        className="relative z-10"
      >
        <GameGrid selectedGame={selectedGame} setSelectedGame={setSelectedGame} />
      </motion.div>

      {/* Mobile Help Section */}
      <div className="md:hidden relative z-10">
        <MobileHelpSection />
      </div>

      {/* Contact Section */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
        viewport={{ once: true, margin: "-100px" }}
        className="relative z-10"
      >
        <ContactSection />
      </motion.div>
    </div>
  );
};

export default Games;
